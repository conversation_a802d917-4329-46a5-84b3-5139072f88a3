@echo off
REM Deinstalliert automatischen Start für vLLM + Agent Zero
echo 🗑️ Deinstalliere automatischen Start für vLLM + Agent Zero...

REM Überprüfe Administrator-Rechte
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Dieses Skript muss als Administrator ausgeführt werden!
    echo Rechtsklick auf die Datei und "Als Administrator ausführen" wählen.
    pause
    exit /b 1
)

echo ✅ Administrator-Rechte erkannt

REM Lösche Autostart-Skript
set "AUTOSTART_SCRIPT=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\vLLM-Agent-Zero-Autostart.bat"
if exist "%AUTOSTART_SCRIPT%" (
    del "%AUTOSTART_SCRIPT%"
    echo ✅ Autostart-Skript gelöscht
) else (
    echo ⚠️  Autostart-Skript nicht gefunden
)

REM Lösche Windows-Aufgabe
echo 📋 Lösche Windows-Aufgabe...
schtasks /delete /tn "vLLM-Agent-Zero-AutoStart" /f >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Windows-Aufgabe gelöscht
) else (
    echo ⚠️  Windows-Aufgabe nicht gefunden oder konnte nicht gelöscht werden
)

REM Docker Desktop Autostart deaktivieren
echo 🐳 Deaktiviere Docker Desktop Autostart...
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "DockerDesktop" /f >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker Desktop Autostart deaktiviert
) else (
    echo ⚠️  Docker Desktop Autostart-Eintrag nicht gefunden
)

echo.
echo 🎉 Autostart-Deinstallation abgeschlossen!
echo.
echo 📋 Was wurde entfernt:
echo   ✅ Autostart-Skript aus Windows-Startup-Ordner
echo   ✅ Windows-Aufgabe für automatischen Start
echo   ✅ Docker Desktop automatischer Start
echo.
echo ℹ️  Die vLLM und Agent Zero Container laufen weiterhin.
echo    Zum Stoppen verwenden Sie: docker-compose down
echo.

pause
