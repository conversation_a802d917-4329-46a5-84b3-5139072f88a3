@echo off
REM Installiert automatischen Start für vLLM + Agent Zero
echo 🚀 Installiere automatischen Start für vLLM + Agent Zero...

REM Überprüfe Administrator-Rechte
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Dieses Skript muss als Administrator ausgeführt werden!
    echo Rechtsklick auf die Datei und "Als Administrator ausführen" wählen.
    pause
    exit /b 1
)

echo ✅ Administrator-Rechte erkannt

REM Erstelle Autostart-Verzeichnis falls nicht vorhanden
set "AUTOSTART_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"
if not exist "%AUTOSTART_DIR%" mkdir "%AUTOSTART_DIR%"

REM Erstelle Autostart-Skript
set "AUTOSTART_SCRIPT=%AUTOSTART_DIR%\vLLM-Agent-Zero-Autostart.bat"

echo @echo off > "%AUTOSTART_SCRIPT%"
echo REM Automatischer Start von vLLM + Agent Zero >> "%AUTOSTART_SCRIPT%"
echo. >> "%AUTOSTART_SCRIPT%"
echo REM Warte bis Windows vollständig gestartet ist >> "%AUTOSTART_SCRIPT%"
echo timeout /t 60 /nobreak ^>nul >> "%AUTOSTART_SCRIPT%"
echo. >> "%AUTOSTART_SCRIPT%"
echo REM Wechsle ins vLLM-Verzeichnis >> "%AUTOSTART_SCRIPT%"
echo cd /d "c:\Users\<USER>\Documents\augment-projects\vllm docker" >> "%AUTOSTART_SCRIPT%"
echo. >> "%AUTOSTART_SCRIPT%"
echo REM Starte vLLM automatisch >> "%AUTOSTART_SCRIPT%"
echo call auto-start-vllm.bat >> "%AUTOSTART_SCRIPT%"

echo ✅ Autostart-Skript erstellt: %AUTOSTART_SCRIPT%

REM Erstelle Windows-Aufgabe für zuverlässigeren Start
echo 📋 Erstelle Windows-Aufgabe...

schtasks /create /tn "vLLM-Agent-Zero-AutoStart" /tr "\"c:\Users\<USER>\Documents\augment-projects\vllm docker\auto-start-vllm.bat\"" /sc onstart /ru "%USERNAME%" /rl highest /f >nul 2>&1

if %errorlevel% equ 0 (
    echo ✅ Windows-Aufgabe erfolgreich erstellt
) else (
    echo ⚠️  Windows-Aufgabe konnte nicht erstellt werden, verwende Autostart-Ordner
)

REM Docker Desktop Autostart konfigurieren
echo 🐳 Konfiguriere Docker Desktop Autostart...

REM Registry-Eintrag für Docker Desktop
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "DockerDesktop" /t REG_SZ /d "\"C:\Program Files\Docker\Docker\Docker Desktop.exe\"" /f >nul 2>&1

if %errorlevel% equ 0 (
    echo ✅ Docker Desktop Autostart aktiviert
) else (
    echo ⚠️  Docker Desktop Autostart konnte nicht aktiviert werden
)

echo.
echo 🎉 Autostart-Installation abgeschlossen!
echo.
echo 📋 Was wurde konfiguriert:
echo   ✅ Autostart-Skript im Windows-Startup-Ordner
echo   ✅ Windows-Aufgabe für zuverlässigen Start
echo   ✅ Docker Desktop automatischer Start
echo.
echo 🔄 Beim nächsten Windows-Start werden automatisch gestartet:
echo   • Docker Desktop
echo   • vLLM Intel XPU Server (Port 8000)
echo   • vLLM Proxy (Port 8080)
echo   • Agent Zero (Port 32768)
echo.
echo 🔧 Zum Deaktivieren:
echo   • Lösche: %AUTOSTART_SCRIPT%
echo   • Führe aus: schtasks /delete /tn "vLLM-Agent-Zero-AutoStart" /f
echo   • Registry: reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "DockerDesktop" /f
echo.

pause
