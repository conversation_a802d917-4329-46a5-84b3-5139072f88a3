#!/usr/bin/env python3
"""
Konfigurationsskript für Agent Zero zur Verwendung von Intel-optimiertem vLLM
"""

import os
import json
import requests
import time
import subprocess

def wait_for_vllm(url="http://localhost:8080", timeout=300):
    """Warte bis vLLM verfügbar ist"""
    print(f"Warte auf vLLM Server unter {url}...")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ vLLM Server ist verfügbar!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        print("⏳ Warte auf vLLM Server...")
        time.sleep(5)
    
    print("❌ Timeout beim Warten auf vLLM Server")
    return False

def test_vllm_api(base_url="http://localhost:8080"):
    """Teste die vLLM API"""
    try:
        # Test Models Endpoint
        models_response = requests.get(f"{base_url}/v1/models")
        if models_response.status_code == 200:
            models = models_response.json()
            print(f"✅ Verfügbare Modelle: {[model['id'] for model in models['data']]}")
        
        # Test Chat Completion
        chat_data = {
            "model": "gpt-3.5-turbo",
            "messages": [
                {"role": "user", "content": "Hallo, kannst du mich hören?"}
            ],
            "max_tokens": 50
        }
        
        chat_response = requests.post(
            f"{base_url}/v1/chat/completions",
            json=chat_data,
            headers={"Content-Type": "application/json"}
        )
        
        if chat_response.status_code == 200:
            result = chat_response.json()
            print(f"✅ Chat Test erfolgreich: {result['choices'][0]['message']['content']}")
            return True
        else:
            print(f"❌ Chat Test fehlgeschlagen: {chat_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API Test fehlgeschlagen: {e}")
        return False

def create_agent_zero_env_config():
    """Erstelle Umgebungsvariablen für Agent Zero"""
    config = {
        "OPENAI_API_BASE": "http://host.docker.internal:8080/v1",
        "OPENAI_API_KEY": "dummy-key-for-vllm",
        "OPENAI_MODEL": "gpt-3.5-turbo",
        "VLLM_ENDPOINT": "http://host.docker.internal:8080"
    }
    
    # Schreibe .env Datei
    with open('.env', 'w') as f:
        for key, value in config.items():
            f.write(f"{key}={value}\n")
    
    print("✅ Agent Zero Konfiguration erstellt (.env)")
    return config

def detect_intel_gpu():
    """Erkenne Intel GPU"""
    try:
        result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name'],
                              capture_output=True, text=True)
        return 'intel' in result.stdout.lower()
    except:
        return False

def main():
    print("🚀 Konfiguriere Agent Zero für Intel-optimiertes vLLM...")

    # Erkenne Hardware
    has_intel_gpu = detect_intel_gpu()
    if has_intel_gpu:
        print("✅ Intel GPU erkannt - verwende XPU optimierte Konfiguration")
        base_url = "http://localhost:8000"
    else:
        print("⚠️  Keine Intel GPU - verwende CPU Fallback")
        base_url = "http://localhost:8001"

    # Erstelle Konfiguration
    config = create_agent_zero_env_config()

    # Warte auf vLLM (Proxy)
    if wait_for_vllm():
        # Teste API
        if test_vllm_api():
            print("\n🎉 Setup erfolgreich abgeschlossen!")
            print("\nNächste Schritte:")
            print("1. Starte Agent Zero Container neu mit: docker restart bfabff266f26")
            print("2. Agent Zero wird automatisch vLLM verwenden")
            print(f"3. vLLM Proxy läuft auf: http://localhost:8080")
            if has_intel_gpu:
                print(f"4. vLLM Intel XPU Server: http://localhost:8000")
            else:
                print(f"4. vLLM CPU Server: http://localhost:8001")
            print(f"5. Agent Zero läuft auf: http://localhost:32768")
        else:
            print("❌ API Test fehlgeschlagen")
    else:
        print("❌ vLLM Server nicht erreichbar")

if __name__ == "__main__":
    main()
