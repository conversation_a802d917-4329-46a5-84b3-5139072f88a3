services:
  # Intel XPU optimierte vLLM Version
  vllm-intel:
    image: intelanalytics/ipex-llm-serving-xpu:latest
    container_name: vllm-intel-server
    ports:
      - "8000:8000"
    environment:
      - VLLM_HOST=0.0.0.0
      - VLLM_PORT=8000
      - HF_HOME=/llm/models
      - IPEX_LLM_LOW_MEM=1
    volumes:
      - ./models:/llm/models
      - /dev/dri:/dev/dri  # Intel GPU device access
    devices:
      - /dev/dri:/dev/dri  # Intel GPU device mapping
    privileged: true  # Für GPU-Zugriff erforderlich
    command: >
      --model microsoft/DialoGPT-medium
      --host 0.0.0.0
      --port 8000
      --served-model-name gpt-3.5-turbo
      --max-model-len 2048
      --dtype float16
      --device xpu
      --tensor-parallel-size 1
      --disable-log-requests
      --trust-remote-code
      --gpu-memory-utilization 0.8
    restart: always
    networks:
      - vllm-network

  # Fallback: Standard CPU vLLM falls Intel XPU nicht verfügbar
  vllm-cpu:
    image: vllm/vllm-openai:latest
    container_name: vllm-cpu-server
    ports:
      - "8001:8000"
    environment:
      - VLLM_HOST=0.0.0.0
      - VLLM_PORT=8000
      - HF_HOME=/root/.cache/huggingface
    volumes:
      - ./models:/root/.cache/huggingface
    command: >
      --model microsoft/DialoGPT-medium
      --host 0.0.0.0
      --port 8000
      --served-model-name gpt-3.5-turbo
      --max-model-len 1024
      --dtype float16
      --device cpu
      --tensor-parallel-size 1
      --disable-log-requests
      --trust-remote-code
    restart: always
    networks:
      - vllm-network
    profiles:
      - fallback

  # Nginx Proxy für Agent Zero Integration
  nginx-proxy:
    image: nginx:alpine
    container_name: vllm-proxy
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - vllm-intel
    restart: always
    networks:
      - vllm-network

networks:
  vllm-network:
    driver: bridge
