services:
  vllm:
    image: vllm/vllm-openai:latest
    container_name: vllm-server
    ports:
      - "8000:8000"
    environment:
      - VLLM_HOST=0.0.0.0
      - VLLM_PORT=8000
      - HF_HOME=/app/models
    volumes:
      - ./models:/app/models
    command: >
      --model microsoft/DialoGPT-medium
      --host 0.0.0.0
      --port 8000
      --served-model-name gpt-3.5-turbo
      --max-model-len 1024
      --dtype float16
      --device cpu
      --tensor-parallel-size 1
      --disable-log-requests
      --trust-remote-code
    restart: unless-stopped
    networks:
      - vllm-network

  # Nginx Proxy für Agent Zero Integration
  nginx-proxy:
    image: nginx:alpine
    container_name: vllm-proxy
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - vllm
    restart: unless-stopped
    networks:
      - vllm-network

networks:
  vllm-network:
    driver: bridge
