# 🚀 vLLM + Agent Zero Autostart Setup

## ✅ Was wurde installiert:

### 1. Intel-optimiertes vLLM Setup
- **Intel XPU Image**: `intelanalytics/ipex-llm-serving-xpu:latest` (8GB Download läuft gerade)
- **CPU Fallback**: Standard vLLM für den Fall, dass Intel GPU nicht verfügbar
- **Nginx Proxy**: Load Balancing und CORS-Handling
- **Automatische Hardware-Erkennung**: Wählt optimale Konfiguration

### 2. Autostart-Skripte
- `auto-start-vllm.bat` - Automatischer Start mit Hardware-Erkennung
- `install-autostart.bat` - Windows Autostart-Installation
- `uninstall-autostart.bat` - Autostart-Deinstallation

### 3. Konfigurationsdateien
- `docker-compose.yml` - Intel XPU + CPU Fallback Services
- `nginx.conf` - Proxy-Konfiguration für Agent Zero
- `agent-zero-config.py` - Automatische Agent Zero Konfiguration

## 🔧 Autostart aktivieren:

### Schritt 1: Als Administrator ausführen
```cmd
# Rechtsklick auf "install-autostart.bat" -> "Als Administrator ausführen"
install-autostart.bat
```

### Schritt 2: Docker Desktop Autostart (optional)
```cmd
# Rechtsklick auf "configure-docker-autostart.ps1" -> "Als Administrator ausführen"
configure-docker-autostart.ps1
```

## 📊 Nach dem Setup verfügbar:

### Ports:
- **vLLM Intel XPU**: `http://localhost:8000`
- **vLLM Proxy**: `http://localhost:8080` (für Agent Zero)
- **Agent Zero**: `http://localhost:32768` (wie gewohnt)

### Automatischer Start beim Windows-Boot:
1. **Docker Desktop** startet automatisch
2. **vLLM Intel XPU** startet automatisch (falls Intel GPU verfügbar)
3. **vLLM CPU Fallback** startet automatisch (falls Intel GPU nicht verfügbar)
4. **Agent Zero** startet automatisch und verwendet vLLM

## 🧪 Testen:

### API-Test:
```bash
curl -X POST "http://localhost:8080/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hallo!"}],
    "max_tokens": 50
  }'
```

### Status überprüfen:
```cmd
docker ps | findstr -i "vllm\|agent"
```

## 🔄 Manueller Start/Stop:

### Starten:
```cmd
docker-compose up -d
python agent-zero-config.py
```

### Stoppen:
```cmd
docker-compose down
```

### Agent Zero neu starten:
```cmd
docker restart bfabff266f26
```

## 🛠️ Troubleshooting:

### vLLM startet nicht:
1. Überprüfen Sie Docker-Ressourcen (mindestens 8GB RAM)
2. Logs prüfen: `docker-compose logs vllm-intel`

### Agent Zero kann vLLM nicht erreichen:
1. Überprüfen Sie: `curl http://localhost:8080/health`
2. Agent Zero neu starten: `docker restart bfabff266f26`

### Autostart funktioniert nicht:
1. Als Administrator ausführen: `install-autostart.bat`
2. Windows-Aufgabe prüfen: `schtasks /query /tn "vLLM-Agent-Zero-AutoStart"`

## 📁 Dateiübersicht:

```
vllm docker/
├── docker-compose.yml          # Intel XPU + CPU Services
├── nginx.conf                  # Proxy-Konfiguration
├── agent-zero-config.py        # Agent Zero Setup
├── auto-start-vllm.bat         # Automatischer Start
├── install-autostart.bat       # Autostart-Installation
├── uninstall-autostart.bat     # Autostart-Deinstallation
├── configure-docker-autostart.ps1  # Docker Autostart
├── setup-autostart.ps1         # PowerShell Autostart
├── requirements.txt            # Python-Abhängigkeiten
├── .env                        # Umgebungsvariablen (generiert)
├── models/                     # Modell-Cache
└── SETUP-ANLEITUNG.md          # Diese Datei
```

## 🎉 Fertig!

Nach dem Download (ca. 8GB) und der Autostart-Installation:
- **Beim nächsten Windows-Start** werden alle Services automatisch gestartet
- **Agent Zero** verwendet automatisch Ihr lokales vLLM
- **Intel-Grafikkarte** wird optimal genutzt (falls verfügbar)
- **CPU-Fallback** funktioniert automatisch

**Nächster Schritt**: Führen Sie `install-autostart.bat` als Administrator aus!
