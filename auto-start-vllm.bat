@echo off
REM Automatisches Startup-Skript für vLLM + Agent Zero
REM Dieses Skript startet vLLM automatisch beim Windows-Start

echo 🚀 Automatischer Start von vLLM für Agent Zero...

REM Wechsle ins vLLM-Verzeichnis
cd /d "c:\Users\<USER>\Documents\augment-projects\vllm docker"

REM Warte kurz, bis Docker Desktop gestartet ist
echo ⏳ Warte auf Docker Desktop...
timeout /t 30 /nobreak

REM Überprüfe ob Docker läuft
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker ist nicht verfügbar. Starte Docker Desktop...
    start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    timeout /t 60 /nobreak
)

REM Starte vLLM Container
echo 🐳 Starte vLLM Container...
docker-compose up -d

REM Warte auf vLLM
echo ⏳ Warte auf vLLM Server...
timeout /t 60 /nobreak

REM Starte Agent Zero falls gestoppt
echo 🤖 Überprüfe Agent Zero Status...
docker ps | findstr bfabff266f26 >nul
if %errorlevel% neq 0 (
    echo 🔄 Starte Agent Zero Container...
    docker start bfabff266f26
)

echo ✅ Automatischer Start abgeschlossen!
echo.
echo 📊 Status:
echo - vLLM Intel XPU: http://localhost:8000
echo - vLLM Proxy: http://localhost:8080  
echo - Agent Zero: http://localhost:32768
echo.

REM Logge Status
docker ps | findstr -i "vllm\|agent"

REM Fenster offen lassen für 10 Sekunden
timeout /t 10 /nobreak
