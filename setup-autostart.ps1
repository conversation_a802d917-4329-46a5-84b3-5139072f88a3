# PowerShell-Skript zur Einrichtung des automatischen Starts von vLLM
# Führen Sie dieses Skript als Administrator aus

Write-Host "🚀 Richte automatischen Start für vLLM + Agent Zero ein..." -ForegroundColor Green

# Pfad zum Batch-Skript
$batchPath = "c:\Users\<USER>\Documents\augment-projects\vllm docker\auto-start-vllm.bat"

# Überprüfe ob Batch-Datei existiert
if (-not (Test-Path $batchPath)) {
    Write-Host "❌ Batch-Datei nicht gefunden: $batchPath" -ForegroundColor Red
    exit 1
}

# Erstelle geplante Aufgabe
$taskName = "vLLM-Agent-Zero-AutoStart"
$description = "Automatischer Start von vLLM und Agent Zero beim Windows-Start"

# Lösche existierende Aufgabe falls vorhanden
try {
    Unregister-ScheduledTask -TaskName $taskName -Confirm:$false -ErrorAction SilentlyContinue
    Write-Host "🗑️ Alte Aufgabe entfernt" -ForegroundColor Yellow
} catch {
    # Ignoriere Fehler wenn Aufgabe nicht existiert
}

# Erst<PERSON> neue Aufgabe
$action = New-ScheduledTaskAction -Execute $batchPath
$trigger = New-ScheduledTaskTrigger -AtStartup
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
$principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive -RunLevel Highest

# Registriere Aufgabe
Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Description $description

Write-Host "✅ Geplante Aufgabe '$taskName' erstellt!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Die folgenden Services starten jetzt automatisch beim Windows-Start:" -ForegroundColor Cyan
Write-Host "   • vLLM Intel XPU Server (Port 8000)" -ForegroundColor White
Write-Host "   • vLLM Proxy (Port 8080)" -ForegroundColor White  
Write-Host "   • Agent Zero (Port 32768)" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Zum Deaktivieren führen Sie aus:" -ForegroundColor Yellow
Write-Host "   Unregister-ScheduledTask -TaskName '$taskName' -Confirm:`$false" -ForegroundColor Gray
Write-Host ""
Write-Host "📊 Zum Testen der Aufgabe:" -ForegroundColor Yellow
Write-Host "   Start-ScheduledTask -TaskName '$taskName'" -ForegroundColor Gray

# Zeige Aufgaben-Details
Get-ScheduledTask -TaskName $taskName | Format-Table -AutoSize
