# PowerShell-Skript zur Konfiguration von Docker Desktop für automatischen Start
# Führen Sie dieses Skript als Administrator aus

Write-Host "🐳 Konfiguriere Docker Desktop für automatischen Start..." -ForegroundColor Green

# Docker Desktop Pfad
$dockerPath = "C:\Program Files\Docker\Docker\Docker Desktop.exe"

# Überprüfe ob Docker Desktop installiert ist
if (-not (Test-Path $dockerPath)) {
    Write-Host "❌ Docker Desktop nicht gefunden unter: $dockerPath" -ForegroundColor Red
    Write-Host "   Bitte installieren Sie Docker Desktop zuerst." -ForegroundColor Yellow
    exit 1
}

# Erstelle Registry-Eintrag für automatischen Start
$regPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run"
$regName = "DockerDesktop"

try {
    # Setze Registry-Eintrag
    Set-ItemProperty -Path $regPath -Name $regName -Value "`"$dockerPath`"" -Force
    Write-Host "✅ Docker Desktop Autostart aktiviert!" -ForegroundColor Green
} catch {
    Write-Host "❌ Fehler beim Setzen des Registry-Eintrags: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Konfiguriere Docker Desktop Settings (falls möglich)
$dockerConfigPath = "$env:APPDATA\Docker\settings.json"

if (Test-Path $dockerConfigPath) {
    try {
        $config = Get-Content $dockerConfigPath | ConvertFrom-Json
        
        # Aktiviere automatischen Start
        $config.openUIOnStartupDisabled = $false
        $config.autoStart = $true
        
        # Speichere Konfiguration
        $config | ConvertTo-Json -Depth 10 | Set-Content $dockerConfigPath
        Write-Host "✅ Docker Desktop Einstellungen aktualisiert!" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Warnung: Konnte Docker-Einstellungen nicht automatisch konfigurieren" -ForegroundColor Yellow
        Write-Host "   Bitte aktivieren Sie 'Start Docker Desktop when you log in' manuell in den Docker-Einstellungen" -ForegroundColor Gray
    }
} else {
    Write-Host "⚠️ Docker-Konfigurationsdatei nicht gefunden" -ForegroundColor Yellow
    Write-Host "   Bitte aktivieren Sie 'Start Docker Desktop when you log in' manuell in den Docker-Einstellungen" -ForegroundColor Gray
}

Write-Host ""
Write-Host "📋 Nächste Schritte:" -ForegroundColor Cyan
Write-Host "   1. Starten Sie Docker Desktop manuell: $dockerPath" -ForegroundColor White
Write-Host "   2. Gehen Sie zu Settings > General" -ForegroundColor White
Write-Host "   3. Aktivieren Sie 'Start Docker Desktop when you log in'" -ForegroundColor White
Write-Host "   4. Führen Sie setup-autostart.ps1 aus, um vLLM Autostart zu aktivieren" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Zum Deaktivieren:" -ForegroundColor Yellow
Write-Host "   Remove-ItemProperty -Path '$regPath' -Name '$regName'" -ForegroundColor Gray
