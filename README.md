# vLLM + Agent Zero Integration

Diese Konfiguration verbindet vLLM mit Agent Zero für lokale LLM-Inferenz mit Intel-Grafikkarten.

## 🚀 Schnellstart

1. **Automatisches Setup ausführen:**
   ```bash
   start-vllm.bat
   ```

2. **<PERSON><PERSON> Setup (falls gewünscht):**
   ```bash
   # vLLM starten
   docker-compose up -d
   
   # Python-Abhängigkeiten installieren
   pip install -r requirements.txt
   
   # Agent Zero konfigurieren
   python agent-zero-config.py
   
   # Agent Zero neu starten
   docker restart bfabff266f26
   ```

## 📋 Komponenten

### vLLM Server
- **Port:** 8080 (Proxy) / 8000 (direkt)
- **Modell:** microsoft/DialoGPT-medium (CPU-optimiert für Intel)
- **API:** OpenAI-kompatibel unter `/v1/`

### Nginx Proxy
- **Zweck:** CORS-Handling und Load Balancing
- **Port:** 8080
- **Features:** Streaming-Support, Health Checks

### Agent Zero Integration
- **Konfiguration:** Automatisch über Umgebungsvariablen
- **Endpoint:** `http://host.docker.internal:8080/v1`
- **Port:** 32768 (wie gewohnt)

## 🔧 Konfiguration

### Umgebungsvariablen (.env)
```
OPENAI_API_BASE=http://host.docker.internal:8080/v1
OPENAI_API_KEY=dummy-key-for-vllm
OPENAI_MODEL=gpt-3.5-turbo
VLLM_ENDPOINT=http://host.docker.internal:8080
```

### Intel GPU Optimierungen
- CPU-Modus für Intel-Grafikkarten
- Float16 Precision für bessere Performance
- Reduzierte max_model_len für Speicher-Effizienz

## 🧪 Tests

### API-Test
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hallo!"}],
    "max_tokens": 50
  }'
```

### Health Check
```bash
curl http://localhost:8080/health
```

## 📊 Monitoring

### Container Status
```bash
docker ps | findstr -i "vllm\|agent"
```

### Logs anzeigen
```bash
# vLLM Logs
docker-compose logs -f vllm

# Nginx Proxy Logs
docker-compose logs -f nginx-proxy
```

## 🔄 Neustart

```bash
# Alles neu starten
docker-compose restart

# Nur vLLM neu starten
docker-compose restart vllm

# Agent Zero neu starten
docker restart bfabff266f26
```

## 🛠️ Troubleshooting

### vLLM startet nicht
- Überprüfen Sie Docker-Ressourcen
- Logs mit `docker-compose logs vllm` prüfen

### Agent Zero kann vLLM nicht erreichen
- Überprüfen Sie die .env Konfiguration
- Testen Sie `curl http://localhost:8080/health`

### Langsame Antworten
- Intel-Grafikkarten sind für CPU-Inferenz optimiert
- Erwägen Sie kleinere Modelle für bessere Performance

## 📁 Dateistruktur

```
vllm docker/
├── docker-compose.yml      # Docker Services
├── nginx.conf             # Proxy Konfiguration
├── agent-zero-config.py   # Setup-Skript
├── start-vllm.bat         # Automatisches Setup
├── requirements.txt       # Python-Abhängigkeiten
├── .env                   # Umgebungsvariablen (generiert)
├── models/                # Modell-Cache (generiert)
└── README.md              # Diese Datei
```
