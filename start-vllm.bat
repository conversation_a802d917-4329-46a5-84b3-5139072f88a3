@echo off
echo 🚀 Starte vLLM für Agent Zero...

echo.
echo 📁 Erstelle Models-Verzeichnis...
if not exist "models" mkdir models

echo.
echo 🐳 Starte vLLM Container...
docker-compose up -d

echo.
echo ⏳ Warte auf vLLM Server...
timeout /t 30 /nobreak

echo.
echo 🔧 Konfiguriere Agent Zero...
python agent-zero-config.py

echo.
echo 🔄 Starte Agent Zero Container neu...
docker restart bfabff266f26

echo.
echo ✅ Setup abgeschlossen!
echo.
echo 📊 Status:
echo - vLLM Server: http://localhost:8080
echo - Agent Zero: http://localhost:32768
echo.
echo 🔍 Überprüfe Container Status:
docker ps | findstr -i "vllm\|agent"

pause
