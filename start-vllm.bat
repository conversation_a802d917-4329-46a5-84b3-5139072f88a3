@echo off
echo 🚀 Starte Intel-optimiertes vLLM für Agent Zero...

echo.
echo 📁 Erstelle Models-Verzeichnis...
if not exist "models" mkdir models

echo.
echo 🔍 Überprüfe Intel GPU Verfügbarkeit...
wmic path win32_VideoController get name | findstr -i "intel" >nul
if %errorlevel%==0 (
    echo ✅ Intel GPU gefunden - verwende Intel XPU optimierte Version
    set USE_INTEL_GPU=true
) else (
    echo ⚠️  Keine Intel GPU gefunden - verwende CPU Fallback
    set USE_INTEL_GPU=false
)

echo.
echo 🐳 Starte vLLM Container...
if "%USE_INTEL_GPU%"=="true" (
    echo Starte Intel XPU optimierte Version...
    docker-compose up -d vllm-intel nginx-proxy
) else (
    echo Starte CPU Fallback Version...
    docker-compose --profile fallback up -d vllm-cpu nginx-proxy
)

echo.
echo ⏳ Warte auf vLLM Server (kann bei erstem Start länger dauern)...
timeout /t 60 /nobreak

echo.
echo 🔧 Konfiguriere Agent Zero...
python agent-zero-config.py

echo.
echo 🔄 Starte Agent Zero Container neu...
docker restart bfabff266f26

echo.
echo ✅ Setup abgeschlossen!
echo.
echo 📊 Status:
if "%USE_INTEL_GPU%"=="true" (
    echo - vLLM Intel XPU Server: http://localhost:8000
) else (
    echo - vLLM CPU Server: http://localhost:8001
)
echo - vLLM Proxy: http://localhost:8080
echo - Agent Zero: http://localhost:32768
echo.
echo 🔍 Überprüfe Container Status:
docker ps | findstr -i "vllm\|agent"

echo.
echo 🧪 Teste vLLM API...
curl -s -X GET http://localhost:8080/health || echo API noch nicht bereit

pause
